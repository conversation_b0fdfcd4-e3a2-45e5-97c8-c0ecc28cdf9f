# 🤖 Microchip AI Chatbot

A modern React TypeScript chatbot application that integrates with the Microchip AI API to provide assistance with microcontrollers, development tools, and products.

## ✨ Features

- **Secure API Key Input**: Safe input with visibility toggle and connection testing
- **Real-time Chat Interface**: Modern chat UI with typing indicators and message timestamps
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Conversation History**: Maintains context for better AI responses
- **Modern UI**: Beautiful gradient design with smooth animations

## 🚀 Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn
- A valid Microchip AI API key

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd newAIScreen
```

2. Install dependencies:
```bash
npm install
```

3. Start both backend and frontend servers:
```bash
npm run dev:full
```

This will start:
- Backend proxy server on `http://localhost:3001`
- Frontend development server on `http://localhost:5173`

Alternatively, you can run them separately:
```bash
# Terminal 1 - Backend server
npm run server

# Terminal 2 - Frontend server
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🔧 Usage

1. **Enter API Key**: When you first open the application, you'll be prompted to enter your Microchip AI API key
2. **Test Connection**: The app will automatically test the connection to ensure your API key is valid
3. **Start Chatting**: Once connected, you can start asking questions about microcontrollers, development tools, and more
4. **Manage Chat**: Use the "Clear" button to reset the conversation or "Change Key" to use a different API key

## 🏗️ Project Structure

```
├── src/                    # Frontend React application
│   ├── components/
│   │   ├── ApiKeyInput.tsx     # API key input component
│   │   ├── ApiKeyInput.css     # Styling for API key input
│   │   ├── Chatbot.tsx         # Main chatbot interface
│   │   └── Chatbot.css         # Styling for chatbot
│   ├── services/
│   │   └── MicrochipAPI.ts     # Frontend API service class
│   ├── App.tsx                 # Main application component
│   ├── App.css                 # Application styling
│   ├── index.css               # Global styles
│   └── main.tsx               # Application entry point
├── server/                 # Backend proxy server
│   └── index.cjs               # Express.js proxy server
├── package.json            # Dependencies and scripts
└── README.md              # Project documentation
```

## 🔑 API Integration

The application uses a **proxy server architecture** to handle CORS restrictions:

### Frontend → Backend Proxy → Microchip AI API

- **Frontend**: Calls local proxy server at `http://localhost:3001/api/chat`
- **Backend Proxy**: Express.js server that forwards requests to Microchip AI API
- **Microchip API**: `https://ai-apps.microchip.com/CodeGPTAPI/api/Chat/CodeCompletion`

This architecture solves CORS issues and provides better error handling and security.

## 🛠️ Technologies Used

- **React 18**: Modern React with hooks
- **TypeScript**: Type-safe development
- **Vite**: Fast build tool and development server
- **CSS3**: Modern styling with animations and gradients
- **Fetch API**: For HTTP requests to the Microchip API

## 📱 Features in Detail

### API Key Management
- Secure input with show/hide toggle
- Real-time validation
- Connection testing before enabling chat

### Chat Interface
- Real-time messaging
- Typing indicators
- Message timestamps
- Auto-scroll to latest messages
- Error handling with retry options

### Responsive Design
- Mobile-friendly interface
- Adaptive layouts
- Touch-friendly controls

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues related to the Microchip AI API, please contact Microchip support.
For application issues, please create an issue in this repository.
