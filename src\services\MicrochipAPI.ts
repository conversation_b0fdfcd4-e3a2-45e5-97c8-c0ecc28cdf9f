export interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
}

export interface APIResponse {
  success: boolean;
  message: string;
  error?: string;
}

export class MicrochipAPI {
  private apiKey: string = '';
  private conversationHistory: string[] = [];
  private readonly baseURL = 'http://localhost:3001';
  private readonly endpoint = '/api/chat';

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey.trim();
  }

  getApiKey(): string {
    return this.apiKey;
  }

  clearHistory(): void {
    this.conversationHistory = [];
  }

  async testConnection(): Promise<APIResponse> {
    if (!this.apiKey) {
      return {
        success: false,
        message: '',
        error: 'API key is required'
      };
    }

    try {
      const response = await this.callAPI('Hello, are you working?');
      return {
        success: true,
        message: response
      };
    } catch (error) {
      return {
        success: false,
        message: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async sendMessage(userMessage: string): Promise<string> {
    if (!this.apiKey) {
      throw new Error('API key is required');
    }

    if (!userMessage.trim()) {
      throw new Error('Message cannot be empty');
    }

    try {
      const response = await this.callAPI(userMessage);
      this.conversationHistory.push(userMessage);
      
      // Keep only last 5 messages for context
      if (this.conversationHistory.length > 5) {
        this.conversationHistory = this.conversationHistory.slice(-5);
      }
      
      return response;
    } catch (error) {
      throw error;
    }
  }

  private async callAPI(userMessage: string): Promise<string> {
    const requestBody = {
      questions: [userMessage],
      answers: this.conversationHistory.slice(-5),
      category: 101,
      logQnA: true,
      client: "react-chatbot",
      apiKey: this.apiKey
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const response = await fetch(`${this.baseURL}${this.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (!response.ok) {
        if (data.error) {
          throw new Error(data.error);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      if (data.success && data.message) {
        return data.message;
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Unexpected response format from server');
      }

    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout - Server took too long to respond');
        } else if (error.message.includes('Failed to fetch')) {
          throw new Error('Network error - Please check if the backend server is running');
        } else {
          throw error;
        }
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }
}

// Create a singleton instance
export const microchipAPI = new MicrochipAPI();
